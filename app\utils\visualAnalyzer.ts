import { extractColorsFromImage, extractColorsFromUrl, getColorEmotion } from './colorExtractor';
import { VisualIdentity } from '../types/brand';
import { analyzeWithGPT } from '../lib/openai';

export async function analyzeVisualIdentity(
  imageFile?: File,
  websiteUrl?: string
): Promise<VisualIdentity> {
  let colorData;
  
  try {
    if (imageFile) {
      colorData = await extractColorsFromImage(imageFile);
    } else if (websiteUrl) {
      // For website screenshots, we'll simulate color extraction
      // In a real implementation, you'd use a service like Puppeteer to screenshot
      colorData = {
        dominant: '#6366f1',
        palette: ['#6366f1', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b'],
        emotions: ['professional', 'trustworthy', 'creative']
      };
    } else {
      // Default color palette if no visual input
      colorData = {
        dominant: '#6366f1',
        palette: ['#6366f1', '#8b5cf6', '#06b6d4'],
        emotions: ['professional', 'modern']
      };
    }

    // Analyze color emotions
    const allEmotions = colorData.palette.flatMap(color => getColorEmotion(color));
    const uniqueEmotions = [...new Set(allEmotions)];
    
    const colorEmotionAnalysis = await analyzeColorEmotions(colorData.palette, uniqueEmotions);
    const similarBrandMatch = await findSimilarBrandStyle(colorData.palette, uniqueEmotions);

    return {
      color_palette: {
        colors: colorData.palette,
        dominant_color: colorData.dominant,
        accent_colors: colorData.palette.slice(1, 3)
      },
      color_emotion_analysis: colorEmotionAnalysis,
      similar_brand_match: similarBrandMatch
    };

  } catch (error) {
    console.error('Visual identity analysis failed:', error);
    
    // Return fallback visual identity
    return {
      color_palette: {
        colors: ['#6366f1', '#8b5cf6', '#06b6d4'],
        dominant_color: '#6366f1',
        accent_colors: ['#8b5cf6', '#06b6d4']
      },
      color_emotion_analysis: 'Professional and modern color scheme',
      similar_brand_match: 'Contemporary digital brand'
    };
  }
}

async function analyzeColorEmotions(colors: string[], emotions: string[]): Promise<string> {
  const prompt = `Analyze this color palette and provide a detailed emotional and brand personality analysis:

Colors: ${colors.join(', ')}
Detected emotions: ${emotions.join(', ')}

Provide a comprehensive 3-4 sentence analysis covering:
1. What emotions and feelings this color palette evokes
2. What type of brand personality it suggests
3. How these colors work together to create a cohesive brand impression
4. What audience or market this palette would appeal to

Be specific and insightful in your analysis.`;

  try {
    const analysis = await analyzeWithGPT(
      prompt,
      'You are a color psychology expert. Provide concise, insightful analysis of color palettes and their emotional impact.'
    );
    
    return analysis || 'This color palette creates a professional and balanced brand impression, suggesting reliability and modern appeal. The colors work harmoniously to convey trustworthiness while maintaining visual interest. This combination would appeal to a broad, professional audience seeking quality and dependability.';
  } catch (error) {
    console.error('Color emotion analysis failed:', error);
    return `${emotions.slice(0, 3).join(', ')} color palette`;
  }
}

async function findSimilarBrandStyle(colors: string[], emotions: string[]): Promise<string> {
  const prompt = `Based on this color palette and emotional characteristics, what type of brand or industry does this visual identity most closely resemble?

Colors: ${colors.join(', ')}
Emotions: ${emotions.join(', ')}

Provide a brief description of the brand style or industry this resembles (e.g., "Tech startup", "Luxury fashion", "Healthcare", "Creative agency", etc.).`;

  try {
    const match = await analyzeWithGPT(
      prompt,
      'You are a brand identity expert. Identify brand styles and industries based on visual characteristics.'
    );
    
    return match || 'Modern digital brand';
  } catch (error) {
    console.error('Similar brand matching failed:', error);
    return 'Contemporary brand style';
  }
}

export function generateColorVariations(baseColor: string): string[] {
  // Simple color variation generator
  const hex = baseColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  const variations = [];
  
  // Lighter variation
  const lighter = `#${Math.min(255, r + 30).toString(16).padStart(2, '0')}${Math.min(255, g + 30).toString(16).padStart(2, '0')}${Math.min(255, b + 30).toString(16).padStart(2, '0')}`;
  variations.push(lighter);
  
  // Darker variation
  const darker = `#${Math.max(0, r - 30).toString(16).padStart(2, '0')}${Math.max(0, g - 30).toString(16).padStart(2, '0')}${Math.max(0, b - 30).toString(16).padStart(2, '0')}`;
  variations.push(darker);
  
  // Complementary color (simplified)
  const comp = `#${(255 - r).toString(16).padStart(2, '0')}${(255 - g).toString(16).padStart(2, '0')}${(255 - b).toString(16).padStart(2, '0')}`;
  variations.push(comp);

  return variations;
}

export function calculateColorHarmony(colors: string[]): number {
  if (colors.length < 2) return 100;

  // Simple harmony calculation based on color distance
  let totalHarmony = 0;
  let comparisons = 0;

  for (let i = 0; i < colors.length; i++) {
    for (let j = i + 1; j < colors.length; j++) {
      const harmony = calculateColorDistance(colors[i], colors[j]);
      totalHarmony += harmony;
      comparisons++;
    }
  }

  return Math.round(totalHarmony / comparisons);
}

function calculateColorDistance(color1: string, color2: string): number {
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');
  
  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);
  
  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);

  const distance = Math.sqrt(
    Math.pow(r2 - r1, 2) + Math.pow(g2 - g1, 2) + Math.pow(b2 - b1, 2)
  );

  // Convert distance to harmony score (0-100)
  const maxDistance = Math.sqrt(3 * Math.pow(255, 2));
  const harmony = 100 - (distance / maxDistance) * 100;
  
  return Math.max(0, Math.min(100, harmony));
}
