import { getColorEmotion } from './colorExtractor';
import { VisualIdentity } from '../types/brand';
import { analyzeWithGPT } from '../lib/openai';

interface ExtractedColors {
  dominant: string;
  palette: string[];
  emotions: string[];
}

export async function analyzeVisualIdentity(
  extractedColors?: ExtractedColors,
  websiteUrl?: string
): Promise<VisualIdentity> {
  let colorData;

  try {
    if (extractedColors) {
      console.log('Using client-extracted colors:', extractedColors);
      colorData = extractedColors;
    } else if (websiteUrl) {
      console.log('Analyzing website for colors...');
      // Generate a more varied color palette based on website URL
      const websiteColors = generateWebsiteColorPalette(websiteUrl);
      colorData = {
        dominant: websiteColors[0],
        palette: websiteColors,
        emotions: websiteColors.flatMap(color => getColorEmotion(color)).slice(0, 5)
      };
    } else {
      console.log('Using default color palette...');
      // More varied default color palette
      colorData = {
        dominant: '#4f46e5',
        palette: ['#4f46e5', '#7c3aed', '#0891b2', '#059669', '#dc2626'],
        emotions: ['professional', 'modern', 'trustworthy']
      };
    }

    // Analyze color emotions
    const allEmotions = colorData.palette.flatMap(color => getColorEmotion(color));
    const uniqueEmotions = [...new Set(allEmotions)];
    
    const colorEmotionAnalysis = await analyzeColorEmotions(colorData.palette, uniqueEmotions);
    const similarBrandMatch = await findSimilarBrandStyle(colorData.palette, uniqueEmotions);

    return {
      color_palette: {
        colors: colorData.palette,
        dominant_color: colorData.dominant,
        accent_colors: colorData.palette.slice(1, 3)
      },
      color_emotion_analysis: colorEmotionAnalysis,
      similar_brand_match: similarBrandMatch
    };

  } catch (error) {
    console.error('Visual identity analysis failed:', error);
    
    // Return fallback visual identity
    return {
      color_palette: {
        colors: ['#6366f1', '#8b5cf6', '#06b6d4'],
        dominant_color: '#6366f1',
        accent_colors: ['#8b5cf6', '#06b6d4']
      },
      color_emotion_analysis: 'Professional and modern color scheme',
      similar_brand_match: 'Contemporary digital brand'
    };
  }
}

async function analyzeColorEmotions(colors: string[], emotions: string[]): Promise<string> {
  const prompt = `Analyze this color palette and provide a detailed emotional and brand personality analysis:

Colors: ${colors.join(', ')}
Detected emotions: ${emotions.join(', ')}

Provide a comprehensive 3-4 sentence analysis covering:
1. What emotions and feelings this color palette evokes
2. What type of brand personality it suggests
3. How these colors work together to create a cohesive brand impression
4. What audience or market this palette would appeal to

Be specific and insightful in your analysis.`;

  try {
    const analysis = await analyzeWithGPT(
      prompt,
      'You are a color psychology expert. Provide concise, insightful analysis of color palettes and their emotional impact.'
    );
    
    return analysis || `This ${colors.length}-color palette creates a ${emotions.includes('professional') ? 'professional and sophisticated' : 'vibrant and engaging'} brand impression. The dominant ${colors[0]} suggests ${emotions[0] || 'reliability'}, while the supporting colors add ${emotions.includes('creative') ? 'creative energy' : 'visual depth and interest'}. This harmonious combination would appeal to audiences seeking ${emotions.includes('trustworthy') ? 'trustworthiness and quality' : 'innovation and authenticity'}, making it ideal for ${emotions.includes('modern') ? 'contemporary digital brands' : 'established professional services'}.`;
  } catch (error) {
    console.error('Color emotion analysis failed:', error);
    return `${emotions.slice(0, 3).join(', ')} color palette`;
  }
}

async function findSimilarBrandStyle(colors: string[], emotions: string[]): Promise<string> {
  const prompt = `Based on this color palette and emotional characteristics, what specific type of brand or industry does this visual identity most closely resemble?

Colors: ${colors.join(', ')}
Emotions: ${emotions.join(', ')}

Provide a specific 2-3 word description of the brand style or industry this resembles. Examples:
- "Tech startup"
- "Luxury fashion"
- "Healthcare provider"
- "Creative agency"
- "Financial services"
- "Wellness brand"
- "Educational platform"
- "E-commerce brand"

Be specific and concise.`;

  try {
    const match = await analyzeWithGPT(
      prompt,
      'You are a brand identity expert. Identify brand styles and industries based on visual characteristics.'
    );
    
    return match || getBrandStyleFromColors(colors, emotions);
  } catch (error) {
    console.error('Similar brand matching failed:', error);
    return 'Contemporary brand style';
  }
}

export function generateColorVariations(baseColor: string): string[] {
  // Simple color variation generator
  const hex = baseColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  const variations = [];
  
  // Lighter variation
  const lighter = `#${Math.min(255, r + 30).toString(16).padStart(2, '0')}${Math.min(255, g + 30).toString(16).padStart(2, '0')}${Math.min(255, b + 30).toString(16).padStart(2, '0')}`;
  variations.push(lighter);
  
  // Darker variation
  const darker = `#${Math.max(0, r - 30).toString(16).padStart(2, '0')}${Math.max(0, g - 30).toString(16).padStart(2, '0')}${Math.max(0, b - 30).toString(16).padStart(2, '0')}`;
  variations.push(darker);
  
  // Complementary color (simplified)
  const comp = `#${(255 - r).toString(16).padStart(2, '0')}${(255 - g).toString(16).padStart(2, '0')}${(255 - b).toString(16).padStart(2, '0')}`;
  variations.push(comp);

  return variations;
}

export function calculateColorHarmony(colors: string[]): number {
  if (colors.length < 2) return 100;

  // Simple harmony calculation based on color distance
  let totalHarmony = 0;
  let comparisons = 0;

  for (let i = 0; i < colors.length; i++) {
    for (let j = i + 1; j < colors.length; j++) {
      const harmony = calculateColorDistance(colors[i], colors[j]);
      totalHarmony += harmony;
      comparisons++;
    }
  }

  return Math.round(totalHarmony / comparisons);
}

function calculateColorDistance(color1: string, color2: string): number {
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');
  
  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);
  
  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);

  const distance = Math.sqrt(
    Math.pow(r2 - r1, 2) + Math.pow(g2 - g1, 2) + Math.pow(b2 - b1, 2)
  );

  // Convert distance to harmony score (0-100)
  const maxDistance = Math.sqrt(3 * Math.pow(255, 2));
  const harmony = 100 - (distance / maxDistance) * 100;
  
  return Math.max(0, Math.min(100, harmony));
}

function generateWebsiteColorPalette(websiteUrl: string): string[] {
  // Generate colors based on website domain/type
  const domain = websiteUrl.toLowerCase();

  if (domain.includes('tech') || domain.includes('ai') || domain.includes('software')) {
    return ['#3b82f6', '#1e40af', '#06b6d4', '#0891b2', '#6366f1'];
  } else if (domain.includes('design') || domain.includes('creative') || domain.includes('art')) {
    return ['#8b5cf6', '#a855f7', '#ec4899', '#f97316', '#eab308'];
  } else if (domain.includes('health') || domain.includes('medical') || domain.includes('wellness')) {
    return ['#10b981', '#059669', '#06b6d4', '#3b82f6', '#6366f1'];
  } else if (domain.includes('finance') || domain.includes('business') || domain.includes('consulting')) {
    return ['#1e40af', '#1e3a8a', '#374151', '#6b7280', '#3b82f6'];
  } else if (domain.includes('food') || domain.includes('restaurant') || domain.includes('cooking')) {
    return ['#f97316', '#ea580c', '#dc2626', '#991b1b', '#eab308'];
  } else {
    // Generate semi-random but professional colors based on URL hash
    const hash = websiteUrl.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);

    const baseHue = Math.abs(hash) % 360;
    return [
      `hsl(${baseHue}, 70%, 50%)`,
      `hsl(${(baseHue + 60) % 360}, 65%, 45%)`,
      `hsl(${(baseHue + 120) % 360}, 60%, 55%)`,
      `hsl(${(baseHue + 180) % 360}, 65%, 50%)`,
      `hsl(${(baseHue + 240) % 360}, 70%, 45%)`
    ].map(hslToHex);
  }
}

function hslToHex(hsl: string): string {
  // Simple HSL to HEX conversion for the generated colors
  const match = hsl.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
  if (!match) return '#6366f1';

  const h = parseInt(match[1]) / 360;
  const s = parseInt(match[2]) / 100;
  const l = parseInt(match[3]) / 100;

  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };

  let r, g, b;
  if (s === 0) {
    r = g = b = l;
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

function getBrandStyleFromColors(colors: string[], emotions: string[]): string {
  // Analyze colors to determine brand style
  const hasBlue = colors.some(c => c.toLowerCase().includes('blue') || c.includes('#0') || c.includes('#3') || c.includes('#4'));
  const hasPurple = colors.some(c => c.toLowerCase().includes('purple') || c.includes('#8') || c.includes('#7'));
  const hasGreen = colors.some(c => c.toLowerCase().includes('green') || c.includes('#0') || c.includes('#1'));
  const hasRed = colors.some(c => c.toLowerCase().includes('red') || c.includes('#d') || c.includes('#e'));
  const hasOrange = colors.some(c => c.toLowerCase().includes('orange') || c.includes('#f'));

  if (emotions.includes('professional') && hasBlue) {
    return 'Corporate services';
  } else if (emotions.includes('creative') && hasPurple) {
    return 'Creative agency';
  } else if (emotions.includes('trustworthy') && hasGreen) {
    return 'Healthcare provider';
  } else if (emotions.includes('energetic') && hasRed) {
    return 'Fitness brand';
  } else if (emotions.includes('friendly') && hasOrange) {
    return 'Food & beverage';
  } else if (emotions.includes('modern')) {
    return 'Tech startup';
  } else if (emotions.includes('elegant')) {
    return 'Luxury brand';
  } else if (emotions.includes('balanced')) {
    return 'Consulting firm';
  } else {
    return 'Digital brand';
  }
}
