import { ConsistencyScore, ToneAnalysis, VisualIdentity, BrandArchetypes } from '../types/brand';
import { calculateColorHarmony } from './visualAnalyzer';

export interface BrandAnalysisData {
  tone_analysis: ToneAnalysis;
  visual_identity: VisualIdentity;
  brand_archetypes: BrandArchetypes;
  content_sources: Array<{
    source: string;
    url: string;
    content: string;
  }>;
}

export function calculateBrandConsistency(data: BrandAnalysisData): ConsistencyScore {
  const toneScore = calculateToneScore(data.tone_analysis, data.content_sources);
  const visualScore = calculateVisualScore(data.visual_identity);
  const messageScore = calculateMessageScore(data.brand_archetypes, data.content_sources);

  // Weighted average: tone 40%, visuals 30%, message 30%
  const overallScore = Math.round(
    (toneScore * 0.4) + (visualScore * 0.3) + (messageScore * 0.3)
  );

  return {
    overall_score: Math.max(1, Math.min(100, overallScore)),
    subscores: {
      tone: Math.max(1, Math.min(100, toneScore)),
      visuals: Math.max(1, Math.min(100, visualScore)),
      message: Math.max(1, Math.min(100, messageScore))
    }
  };
}

function calculateToneScore(toneAnalysis: ToneAnalysis, contentSources: Array<{ source: string; url: string; content: string }>): number {
  let score = 70; // Base score

  // Check for tone clarity and definition
  if (toneAnalysis.tone_summary && toneAnalysis.tone_summary.length > 50) {
    score += 10; // Well-defined tone
  }

  // Check for consistent phrases
  if (toneAnalysis.common_phrases && toneAnalysis.common_phrases.length >= 3) {
    score += 10; // Good phrase consistency
  }

  // Check slider balance (not too extreme)
  const sliders = toneAnalysis.sliders;
  const extremeSliders = Object.values(sliders).filter(value => value < 20 || value > 80);
  if (extremeSliders.length === 0) {
    score += 10; // Balanced tone
  } else if (extremeSliders.length === 1) {
    score += 5; // Mostly balanced
  }

  // Penalize if multiple content sources show inconsistency
  if (contentSources.length > 1) {
    const contentVariety = new Set(contentSources.map(s => s.source)).size;
    if (contentVariety >= 2) {
      score += 5; // Multi-platform presence
    }
  }

  return Math.max(1, Math.min(100, score));
}

function calculateVisualScore(visualIdentity: VisualIdentity): number {
  let score = 60; // Base score

  const colors = visualIdentity.color_palette.colors;
  
  // Check color palette size
  if (colors.length >= 3 && colors.length <= 6) {
    score += 15; // Good palette size
  } else if (colors.length >= 2) {
    score += 10; // Acceptable palette size
  }

  // Check for color harmony
  const harmonyScore = calculateColorHarmony(colors);
  score += Math.round(harmonyScore * 0.2); // Up to 20 points for harmony

  // Check for dominant color definition
  if (visualIdentity.color_palette.dominant_color) {
    score += 10; // Clear dominant color
  }

  // Check for accent colors
  if (visualIdentity.color_palette.accent_colors && 
      visualIdentity.color_palette.accent_colors.length >= 1) {
    score += 10; // Good accent color usage
  }

  // Bonus for emotion analysis
  if (visualIdentity.color_emotion_analysis && 
      visualIdentity.color_emotion_analysis.length > 20) {
    score += 5; // Thoughtful color choices
  }

  return Math.max(1, Math.min(100, score));
}

function calculateMessageScore(brandArchetypes: BrandArchetypes, contentSources: Array<{ source: string; url: string; content: string }>): number {
  let score = 65; // Base score

  // Check primary archetype strength
  if (brandArchetypes.primary_archetype.percentage >= 60) {
    score += 15; // Strong primary archetype
  } else if (brandArchetypes.primary_archetype.percentage >= 40) {
    score += 10; // Moderate primary archetype
  } else {
    score += 5; // Weak primary archetype
  }

  // Check archetype distribution
  const topThreeArchetypes = brandArchetypes.all_archetypes
    .sort((a, b) => b.percentage - a.percentage)
    .slice(0, 3);
  
  const topThreeTotal = topThreeArchetypes.reduce((sum, arch) => sum + arch.percentage, 0);
  
  if (topThreeTotal >= 150) {
    score += 10; // Good archetype focus
  } else if (topThreeTotal >= 120) {
    score += 5; // Moderate archetype focus
  }

  // Check for secondary archetype clarity
  if (brandArchetypes.secondary_archetype && 
      brandArchetypes.secondary_archetype.percentage >= 30) {
    score += 5; // Clear secondary archetype
  }

  // Content consistency bonus
  if (contentSources.length > 1) {
    const avgContentLength = contentSources.reduce((sum, source) => 
      sum + source.content.length, 0) / contentSources.length;
    
    if (avgContentLength > 200) {
      score += 5; // Substantial content across platforms
    }
  }

  return Math.max(1, Math.min(100, score));
}

export function generateConsistencyInsights(score: ConsistencyScore): string[] {
  const insights: string[] = [];

  // Overall score insights
  if (score.overall_score >= 80) {
    insights.push('Your brand shows excellent consistency across all touchpoints.');
  } else if (score.overall_score >= 60) {
    insights.push('Your brand has good consistency with room for improvement.');
  } else {
    insights.push('Your brand consistency needs attention to strengthen your identity.');
  }

  // Tone insights
  if (score.subscores.tone < 60) {
    insights.push('Consider developing a more consistent tone of voice across platforms.');
  } else if (score.subscores.tone >= 80) {
    insights.push('Your tone of voice is well-established and consistent.');
  }

  // Visual insights
  if (score.subscores.visuals < 60) {
    insights.push('Your visual identity could benefit from more consistent color usage.');
  } else if (score.subscores.visuals >= 80) {
    insights.push('Your visual identity is cohesive and well-defined.');
  }

  // Message insights
  if (score.subscores.message < 60) {
    insights.push('Your brand messaging could be more focused and consistent.');
  } else if (score.subscores.message >= 80) {
    insights.push('Your brand message is clear and consistently communicated.');
  }

  return insights;
}

export function identifyConsistencyGaps(score: ConsistencyScore): Array<{
  area: string;
  severity: 'high' | 'medium' | 'low';
  recommendation: string;
}> {
  const gaps = [];

  // Check each subscore for gaps
  if (score.subscores.tone < 70) {
    gaps.push({
      area: 'tone',
      severity: score.subscores.tone < 50 ? 'high' : 'medium',
      recommendation: 'Develop a clear brand voice guide and apply it consistently across all content.'
    });
  }

  if (score.subscores.visuals < 70) {
    gaps.push({
      area: 'visuals',
      severity: score.subscores.visuals < 50 ? 'high' : 'medium',
      recommendation: 'Create a visual style guide with consistent colors, fonts, and imagery.'
    });
  }

  if (score.subscores.message < 70) {
    gaps.push({
      area: 'message',
      severity: score.subscores.message < 50 ? 'high' : 'medium',
      recommendation: 'Clarify your brand positioning and ensure consistent messaging across platforms.'
    });
  }

  // Check for large gaps between subscores
  const maxScore = Math.max(score.subscores.tone, score.subscores.visuals, score.subscores.message);
  const minScore = Math.min(score.subscores.tone, score.subscores.visuals, score.subscores.message);
  
  if (maxScore - minScore > 30) {
    gaps.push({
      area: 'overall',
      severity: 'medium',
      recommendation: 'Focus on balancing all aspects of your brand identity for better overall consistency.'
    });
  }

  return gaps;
}
