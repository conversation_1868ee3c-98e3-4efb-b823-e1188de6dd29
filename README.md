# Brand Audit Lite 🎯

A powerful AI-driven web application that provides creators with comprehensive brand analysis across their digital presence. Get insights into your tone, visual identity, brand consistency, and receive actionable recommendations to strengthen your personal brand.

![Brand Audit Lite](https://img.shields.io/badge/Next.js-15.3.5-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.0-38B2AC?style=for-the-badge&logo=tailwind-css)
![OpenAI](https://img.shields.io/badge/OpenAI-GPT--4-412991?style=for-the-badge&logo=openai)

## ✨ Features

### 🎯 **Tone & Voice Analysis**
- AI-powered analysis of your content across platforms
- Personality sliders (Formal/Casual, Inspiring/Humble, Friendly/Professional)
- Common phrases and language pattern identification
- Tone consistency scoring

### 🎨 **Visual Identity Extraction**
- Color palette extraction from uploaded images and websites
- Dominant color identification and accent color suggestions
- Color emotion analysis and psychology insights
- Similar brand style matching

### 🏛️ **Brand Archetype Matching**
- Identification of your primary and secondary brand archetypes
- Percentage breakdown across all 12 brand archetypes
- Detailed explanations of archetype alignment
- Archetype-specific recommendations

### 📊 **Brand Consistency Scoring**
- Overall consistency score (1-100)
- Detailed subscores for tone, visuals, and messaging
- Cross-platform consistency analysis
- Gap identification and improvement areas

### 💡 **Personalized Suggestions**
- AI-generated actionable improvement tips
- Priority-based recommendation system
- Regeneratable suggestions for fresh perspectives
- Category-specific advice (tone, visuals, messaging, consistency)

### 📤 **Shareable Brand Snapshots**
- Generate visual brand summary cards
- Multiple social media formats (Instagram, Twitter, LinkedIn)
- Downloadable PNG files
- One-click social sharing

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- OpenAI API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd impressly
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Fill in your environment variables:
   ```env
   OPENAI_API_KEY=sk-your_openai_api_key_here
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your_nextauth_secret_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **AI Integration**: OpenAI GPT-4
- **Form Handling**: React Hook Form + Zod validation
- **Image Processing**: ColorThief, Canvas API
- **Web Scraping**: Cheerio, Axios
- **Authentication**: NextAuth.js (ready for implementation)
- **Deployment**: Vercel (recommended)

## 📁 Project Structure

```
impressly/
├── app/
│   ├── api/
│   │   └── analyze/          # Brand analysis API endpoint
│   ├── components/
│   │   ├── ui/              # Reusable UI components
│   │   └── forms/           # Form components
│   ├── lib/                 # Core utilities
│   ├── types/               # TypeScript type definitions
│   ├── utils/               # Analysis utilities
│   ├── results/             # Results page
│   └── page.tsx             # Homepage
├── public/                  # Static assets
└── README.md
```

## 🔧 Configuration

### OpenAI Setup
1. Get your API key from [OpenAI Platform](https://platform.openai.com/)
2. Add it to your `.env.local` file
3. Ensure you have sufficient credits for API calls

### Customization
- **Colors**: Modify the color palette in `tailwind.config.js`
- **Brand Archetypes**: Extend archetypes in `utils/archetypeAnalyzer.ts`
- **Scoring Algorithm**: Adjust weights in `utils/consistencyScorer.ts`
- **Suggestions**: Customize prompts in `utils/suggestionsEngine.ts`

## 🎨 Usage

1. **Start Analysis**: Click "Start Your Brand Audit" on the homepage
2. **Input Information**:
   - Add social media profile URLs (Instagram, TikTok, LinkedIn)
   - Include your website URL
   - Upload a logo or brand image (optional)
3. **Wait for Analysis**: AI processes your content (30-60 seconds)
4. **Review Results**: Comprehensive dashboard with scores and insights
5. **Generate Cards**: Create shareable brand snapshots
6. **Download & Share**: Save cards and share on social media

## 📊 Analysis Components

### Tone Analysis
- Analyzes language patterns and communication style
- Generates personality dimension scores
- Identifies recurring phrases and expressions

### Visual Identity
- Extracts color palettes from images and websites
- Provides color psychology insights
- Suggests brand style similarities

### Brand Archetypes
- Maps content to 12 classical brand archetypes
- Provides percentage breakdowns and explanations
- Offers archetype-specific recommendations

### Consistency Scoring
- Evaluates brand coherence across platforms
- Identifies gaps and improvement opportunities
- Provides actionable insights for enhancement

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
- **Netlify**: Configure build settings for Next.js
- **Railway**: Use Next.js template
- **DigitalOcean**: Deploy using App Platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT-4 API
- Vercel for Next.js and hosting platform
- Tailwind CSS for styling framework
- ColorThief for color extraction
- The open-source community for various utilities

## 📞 Support

If you encounter any issues or have questions:
- Open an issue on GitHub
- Check the documentation
- Review the troubleshooting guide

---

**Built with ❤️ using Next.js, TypeScript, and OpenAI GPT-4**
