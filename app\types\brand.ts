// Brand audit related types

export interface BrandInputs {
  instagram_url?: string;
  tiktok_url?: string;
  linkedin_url?: string;
  website_url?: string;
  image_upload?: File;
}

export interface ToneAnalysis {
  tone_summary: string;
  common_phrases: string[];
  sliders: {
    formal_casual: number; // 0-100
    inspiring_humble: number; // 0-100
    friendly_professional: number; // 0-100
  };
}

export interface ColorPalette {
  colors: string[]; // hex colors
  dominant_color: string;
  accent_colors: string[];
}

export interface VisualIdentity {
  color_palette: ColorPalette;
  color_emotion_analysis: string;
  similar_brand_match?: string;
}

export interface BrandArchetype {
  name: string;
  percentage: number;
  description: string;
}

export interface BrandArchetypes {
  primary_archetype: BrandArchetype;
  secondary_archetype?: BrandArchetype;
  all_archetypes: BrandArchetype[];
}

export interface ConsistencyScore {
  overall_score: number; // 1-100
  subscores: {
    tone: number;
    visuals: number;
    message: number;
  };
}

export interface BrandSuggestion {
  category: string;
  suggestion: string;
  priority: 'high' | 'medium' | 'low';
}

export interface BrandAuditResult {
  id: string;
  inputs: BrandInputs;
  tone_analysis: ToneAnalysis;
  visual_identity: VisualIdentity;
  brand_archetypes: BrandArchetypes;
  consistency_score: ConsistencyScore;
  suggestions: BrandSuggestion[];
  created_at: Date;
}

export interface ShareableCard {
  format: 'instagram' | 'twitter' | 'linkedin';
  image_url: string;
  download_url: string;
}
