import { BrandAuditResult, ShareableCard } from '../types/brand';

export interface CardConfig {
  format: 'instagram' | 'twitter' | 'linkedin';
  width: number;
  height: number;
  backgroundColor: string;
  textColor: string;
}

const CARD_CONFIGS: Record<string, CardConfig> = {
  instagram: {
    format: 'instagram',
    width: 1080,
    height: 1080,
    backgroundColor: '#ffffff',
    textColor: '#1f2937'
  },
  twitter: {
    format: 'twitter',
    width: 1200,
    height: 675,
    backgroundColor: '#ffffff',
    textColor: '#1f2937'
  },
  linkedin: {
    format: 'linkedin',
    width: 1200,
    height: 627,
    backgroundColor: '#ffffff',
    textColor: '#1f2937'
  }
};

export async function generateBrandCard(
  results: BrandAuditResult,
  format: 'instagram' | 'twitter' | 'linkedin'
): Promise<ShareableCard> {
  const config = CARD_CONFIGS[format];
  
  try {
    // Create canvas element
    const canvas = document.createElement('canvas');
    canvas.width = config.width;
    canvas.height = config.height;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }

    // Draw background
    ctx.fillStyle = config.backgroundColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw gradient background
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, results.visual_identity.color_palette.dominant_color + '20');
    gradient.addColorStop(1, results.visual_identity.color_palette.accent_colors[0] + '20');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Set text properties
    ctx.fillStyle = config.textColor;
    ctx.textAlign = 'center';

    // Draw title
    ctx.font = 'bold 48px Arial';
    ctx.fillText('Brand Audit Results', canvas.width / 2, 100);

    // Draw overall score
    ctx.font = 'bold 120px Arial';
    ctx.fillStyle = results.visual_identity.color_palette.dominant_color;
    ctx.fillText(results.consistency_score.overall_score.toString(), canvas.width / 2, 250);
    
    ctx.font = '32px Arial';
    ctx.fillStyle = config.textColor;
    ctx.fillText('Overall Brand Score', canvas.width / 2, 290);

    // Draw subscores
    const subscoreY = 380;
    const subscoreSpacing = canvas.width / 4;
    
    ctx.font = 'bold 36px Arial';
    ctx.fillStyle = results.visual_identity.color_palette.dominant_color;
    
    // Tone score
    ctx.fillText(results.consistency_score.subscores.tone.toString(), subscoreSpacing, subscoreY);
    ctx.font = '20px Arial';
    ctx.fillStyle = config.textColor;
    ctx.fillText('Tone', subscoreSpacing, subscoreY + 30);

    // Visual score
    ctx.font = 'bold 36px Arial';
    ctx.fillStyle = results.visual_identity.color_palette.dominant_color;
    ctx.fillText(results.consistency_score.subscores.visuals.toString(), subscoreSpacing * 2, subscoreY);
    ctx.font = '20px Arial';
    ctx.fillStyle = config.textColor;
    ctx.fillText('Visuals', subscoreSpacing * 2, subscoreY + 30);

    // Message score
    ctx.font = 'bold 36px Arial';
    ctx.fillStyle = results.visual_identity.color_palette.dominant_color;
    ctx.fillText(results.consistency_score.subscores.message.toString(), subscoreSpacing * 3, subscoreY);
    ctx.font = '20px Arial';
    ctx.fillStyle = config.textColor;
    ctx.fillText('Message', subscoreSpacing * 3, subscoreY + 30);

    // Draw primary archetype
    ctx.font = 'bold 32px Arial';
    ctx.fillStyle = config.textColor;
    ctx.fillText('Primary Brand Archetype', canvas.width / 2, 480);
    
    ctx.font = 'bold 40px Arial';
    ctx.fillStyle = results.visual_identity.color_palette.dominant_color;
    ctx.fillText(results.brand_archetypes.primary_archetype.name, canvas.width / 2, 530);

    // Draw color palette
    const colorY = 600;
    const colorSize = 60;
    const colorSpacing = 80;
    const startX = (canvas.width - (results.visual_identity.color_palette.colors.length * colorSpacing)) / 2;

    results.visual_identity.color_palette.colors.forEach((color, index) => {
      ctx.fillStyle = color;
      ctx.fillRect(startX + (index * colorSpacing), colorY, colorSize, colorSize);
      
      // Add border
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 2;
      ctx.strokeRect(startX + (index * colorSpacing), colorY, colorSize, colorSize);
    });

    // Draw footer
    ctx.font = '24px Arial';
    ctx.fillStyle = '#6b7280';
    ctx.fillText('Generated by Brand Audit Lite', canvas.width / 2, canvas.height - 50);

    // Convert to blob and create URLs
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, 'image/png');
    });

    const imageUrl = URL.createObjectURL(blob);
    const downloadUrl = imageUrl; // Same URL for download

    return {
      format,
      image_url: imageUrl,
      download_url: downloadUrl
    };

  } catch (error) {
    console.error('Card generation failed:', error);
    throw new Error('Failed to generate shareable card');
  }
}

export function downloadCard(card: ShareableCard, filename?: string) {
  const link = document.createElement('a');
  link.href = card.download_url;
  link.download = filename || `brand-audit-${card.format}.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

export function shareToSocialMedia(card: ShareableCard, platform: string) {
  const text = encodeURIComponent('Check out my brand audit results! 🎯');
  
  const shareUrls = {
    twitter: `https://twitter.com/intent/tweet?text=${text}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`
  };

  const url = shareUrls[platform as keyof typeof shareUrls];
  if (url) {
    window.open(url, '_blank', 'width=600,height=400');
  }
}

export function getCardDimensions(format: 'instagram' | 'twitter' | 'linkedin') {
  return CARD_CONFIGS[format];
}

export function generateCardPreview(results: BrandAuditResult): string {
  // Generate a simple HTML preview
  return `
    <div style="
      width: 300px; 
      height: 300px; 
      background: linear-gradient(135deg, ${results.visual_identity.color_palette.dominant_color}20, ${results.visual_identity.color_palette.accent_colors[0]}20);
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      font-family: Arial, sans-serif;
      border: 1px solid #e5e7eb;
    ">
      <h3 style="margin: 0 0 10px 0; color: #1f2937;">Brand Audit Results</h3>
      <div style="font-size: 48px; font-weight: bold; color: ${results.visual_identity.color_palette.dominant_color}; margin: 20px 0;">
        ${results.consistency_score.overall_score}
      </div>
      <div style="color: #6b7280; font-size: 14px; margin-bottom: 20px;">Overall Score</div>
      <div style="color: #1f2937; font-weight: bold; margin-bottom: 10px;">
        ${results.brand_archetypes.primary_archetype.name}
      </div>
      <div style="display: flex; justify-content: center; gap: 5px; margin-top: 15px;">
        ${results.visual_identity.color_palette.colors.slice(0, 4).map(color => 
          `<div style="width: 20px; height: 20px; background: ${color}; border-radius: 50%; border: 1px solid #e5e7eb;"></div>`
        ).join('')}
      </div>
    </div>
  `;
}
