import { analyzeWithStructuredOutput } from '../lib/openai';
import { ToneAnalysis } from '../types/brand';

export async function analyzeToneAndVoice(content: string): Promise<ToneAnalysis> {
  const systemPrompt = `You are an expert brand voice analyst. Analyze the provided content and return a detailed tone analysis in JSON format.

  Focus on:
  1. Overall tone and voice characteristics
  2. Common phrases and language patterns
  3. Personality dimensions on specific sliders

  Return the analysis in this exact JSON structure:
  {
    "tone_summary": "A comprehensive 2-3 sentence description of the overall tone and voice",
    "common_phrases": ["phrase1", "phrase2", "phrase3", "phrase4", "phrase5"],
    "sliders": {
      "formal_casual": 0-100,
      "inspiring_humble": 0-100,
      "friendly_professional": 0-100
    }
  }

  Slider explanations:
  - formal_casual: 0 = very formal/corporate, 100 = very casual/conversational
  - inspiring_humble: 0 = very inspiring/motivational, 100 = very humble/modest
  - friendly_professional: 0 = very friendly/personal, 100 = very professional/distant`;

  const userPrompt = `Analyze the tone and voice of this content:

  ${content}

  Provide insights into:
  1. The overall communication style and personality
  2. Recurring phrases or expressions that define the voice
  3. Where this voice falls on the personality dimension sliders
  4. How consistent the tone is across different pieces of content`;

  try {
    const analysis = await analyzeWithStructuredOutput<ToneAnalysis>(
      userPrompt,
      systemPrompt,
      {}
    );

    // Validate and ensure proper structure
    return {
      tone_summary: analysis.tone_summary || 'Unable to determine tone from provided content',
      common_phrases: Array.isArray(analysis.common_phrases) 
        ? analysis.common_phrases.slice(0, 5) 
        : [],
      sliders: {
        formal_casual: Math.max(0, Math.min(100, analysis.sliders?.formal_casual || 50)),
        inspiring_humble: Math.max(0, Math.min(100, analysis.sliders?.inspiring_humble || 50)),
        friendly_professional: Math.max(0, Math.min(100, analysis.sliders?.friendly_professional || 50))
      }
    };
  } catch (error) {
    console.error('Tone analysis failed:', error);
    
    // Return fallback analysis
    return {
      tone_summary: 'Unable to analyze tone due to processing error',
      common_phrases: [],
      sliders: {
        formal_casual: 50,
        inspiring_humble: 50,
        friendly_professional: 50
      }
    };
  }
}

export function extractKeyPhrases(content: string): string[] {
  // Simple phrase extraction as fallback
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const phrases: string[] = [];
  
  // Look for common patterns
  const patterns = [
    /\b(I'm|I am)\s+([^.!?]{1,30})/gi,
    /\b(Let's|Let us)\s+([^.!?]{1,30})/gi,
    /\b(Happy to|Excited to|Love to)\s+([^.!?]{1,30})/gi,
    /\b(Thanks|Thank you)\s+([^.!?]{1,20})/gi,
    /\b(Check out|Take a look)\s+([^.!?]{1,20})/gi
  ];

  patterns.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      phrases.push(...matches.slice(0, 2));
    }
  });

  return phrases.slice(0, 5);
}

export function calculateToneConsistency(analyses: ToneAnalysis[]): number {
  if (analyses.length < 2) return 100;

  let totalVariance = 0;
  const sliderKeys: (keyof ToneAnalysis['sliders'])[] = ['formal_casual', 'inspiring_humble', 'friendly_professional'];

  sliderKeys.forEach(key => {
    const values = analyses.map(a => a.sliders[key]);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    totalVariance += variance;
  });

  const avgVariance = totalVariance / sliderKeys.length;
  const consistencyScore = Math.max(0, 100 - (avgVariance / 10)); // Scale variance to 0-100

  return Math.round(consistencyScore);
}
