import { analyzeWithStructuredOutput } from '../lib/openai';
import { BrandSuggestion, ConsistencyScore, ToneAnalysis, VisualIdentity, BrandArchetypes } from '../types/brand';
import { identifyConsistencyGaps } from './consistencyScorer';
import { getArchetypeRecommendations } from './archetypeAnalyzer';

export interface SuggestionContext {
  tone_analysis: ToneAnalysis;
  visual_identity: VisualIdentity;
  brand_archetypes: BrandArchetypes;
  consistency_score: ConsistencyScore;
  content_sources: Array<{
    source: string;
    url: string;
    content: string;
  }>;
}

export async function generatePersonalizedSuggestions(
  context: SuggestionContext
): Promise<BrandSuggestion[]> {
  try {
    // Get AI-generated suggestions
    const aiSuggestions = await getAISuggestions(context);
    
    // Get rule-based suggestions
    const ruleSuggestions = getRuleBasedSuggestions(context);
    
    // Combine and prioritize suggestions
    const allSuggestions = [...aiSuggestions, ...ruleSuggestions];
    const prioritizedSuggestions = prioritizeSuggestions(allSuggestions, context);
    
    // Return top 5 suggestions
    return prioritizedSuggestions.slice(0, 5);
    
  } catch (error) {
    console.error('Suggestion generation failed:', error);
    return getFallbackSuggestions(context);
  }
}

async function getAISuggestions(context: SuggestionContext): Promise<BrandSuggestion[]> {
  const systemPrompt = `You are a brand strategy expert. Based on the brand analysis provided, generate specific, actionable suggestions for improvement.

  Return suggestions in this exact JSON structure:
  {
    "suggestions": [
      {
        "category": "tone|visuals|messaging|consistency",
        "suggestion": "Specific, actionable advice (1-2 sentences)",
        "priority": "high|medium|low"
      }
    ]
  }

  Focus on:
  1. Specific, actionable recommendations
  2. Areas with the lowest consistency scores
  3. Opportunities to strengthen the primary brand archetype
  4. Ways to improve cross-platform consistency`;

  const userPrompt = `Analyze this brand audit and provide improvement suggestions:

  TONE ANALYSIS:
  - Summary: ${context.tone_analysis.tone_summary}
  - Common phrases: ${context.tone_analysis.common_phrases.join(', ')}
  - Formal/Casual: ${context.tone_analysis.sliders.formal_casual}%
  - Inspiring/Humble: ${context.tone_analysis.sliders.inspiring_humble}%
  - Friendly/Professional: ${context.tone_analysis.sliders.friendly_professional}%

  BRAND ARCHETYPES:
  - Primary: ${context.brand_archetypes.primary_archetype.name} (${context.brand_archetypes.primary_archetype.percentage}%)
  - Secondary: ${context.brand_archetypes.secondary_archetype?.name || 'None'} (${context.brand_archetypes.secondary_archetype?.percentage || 0}%)

  VISUAL IDENTITY:
  - Dominant color: ${context.visual_identity.color_palette.dominant_color}
  - Color palette: ${context.visual_identity.color_palette.colors.join(', ')}
  - Emotion analysis: ${context.visual_identity.color_emotion_analysis}

  CONSISTENCY SCORES:
  - Overall: ${context.consistency_score.overall_score}/100
  - Tone: ${context.consistency_score.subscores.tone}/100
  - Visuals: ${context.consistency_score.subscores.visuals}/100
  - Message: ${context.consistency_score.subscores.message}/100

  CONTENT SOURCES: ${context.content_sources.length} platforms analyzed

  Provide 4-6 specific, actionable suggestions for improvement.`;

  try {
    const response = await analyzeWithStructuredOutput<{ suggestions: BrandSuggestion[] }>(
      userPrompt,
      systemPrompt
    );

    return response.suggestions || [];
  } catch (error) {
    console.error('AI suggestion generation failed:', error);
    return [];
  }
}

function getRuleBasedSuggestions(context: SuggestionContext): BrandSuggestion[] {
  const suggestions: BrandSuggestion[] = [];
  const gaps = identifyConsistencyGaps(context.consistency_score);

  // Add suggestions based on consistency gaps
  gaps.forEach(gap => {
    suggestions.push({
      category: gap.area as 'tone' | 'visuals' | 'messaging' | 'consistency',
      suggestion: gap.recommendation,
      priority: gap.severity
    });
  });

  // Add archetype-specific suggestions
  const archetypeRecs = getArchetypeRecommendations(context.brand_archetypes.primary_archetype);
  archetypeRecs.forEach(rec => {
    suggestions.push({
      category: 'messaging',
      suggestion: rec,
      priority: 'medium'
    });
  });

  // Add tone-specific suggestions
  if (context.tone_analysis.common_phrases.length < 3) {
    suggestions.push({
      category: 'tone',
      suggestion: 'Develop more consistent key phrases and expressions to strengthen your brand voice.',
      priority: 'medium'
    });
  }

  // Add visual-specific suggestions
  if (context.visual_identity.color_palette.colors.length < 3) {
    suggestions.push({
      category: 'visuals',
      suggestion: 'Expand your color palette to include 3-5 consistent colors for better visual identity.',
      priority: 'medium'
    });
  }

  // Add multi-platform suggestions
  if (context.content_sources.length === 1) {
    suggestions.push({
      category: 'consistency',
      suggestion: 'Expand your presence to multiple platforms to strengthen your brand reach and consistency.',
      priority: 'low'
    });
  }

  return suggestions;
}

function prioritizeSuggestions(
  suggestions: BrandSuggestion[],
  context: SuggestionContext
): BrandSuggestion[] {
  // Remove duplicates
  const uniqueSuggestions = suggestions.filter((suggestion, index, self) =>
    index === self.findIndex(s => s.suggestion === suggestion.suggestion)
  );

  // Sort by priority and relevance
  return uniqueSuggestions.sort((a, b) => {
    // Priority order: high > medium > low
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    
    if (priorityDiff !== 0) return priorityDiff;

    // Secondary sort by category relevance based on lowest scores
    const categoryScores: Record<string, number> = {
      tone: context.consistency_score.subscores.tone,
      visuals: context.consistency_score.subscores.visuals,
      messaging: context.consistency_score.subscores.message,
      consistency: context.consistency_score.overall_score
    };

    const aScore = categoryScores[a.category] || 50;
    const bScore = categoryScores[b.category] || 50;
    
    return aScore - bScore; // Lower scores first (need more improvement)
  });
}

function getFallbackSuggestions(context: SuggestionContext): BrandSuggestion[] {
  const fallbackSuggestions: BrandSuggestion[] = [
    {
      category: 'tone',
      suggestion: 'Develop a consistent brand voice that reflects your personality across all platforms.',
      priority: 'high'
    },
    {
      category: 'visuals',
      suggestion: 'Create a cohesive visual identity with consistent colors and imagery.',
      priority: 'high'
    },
    {
      category: 'messaging',
      suggestion: 'Clarify your unique value proposition and communicate it consistently.',
      priority: 'medium'
    },
    {
      category: 'consistency',
      suggestion: 'Audit your content regularly to ensure brand consistency across platforms.',
      priority: 'medium'
    }
  ];

  // Customize based on lowest score
  const lowestScore = Math.min(
    context.consistency_score.subscores.tone,
    context.consistency_score.subscores.visuals,
    context.consistency_score.subscores.message
  );

  if (lowestScore === context.consistency_score.subscores.tone) {
    fallbackSuggestions[0].priority = 'high';
  } else if (lowestScore === context.consistency_score.subscores.visuals) {
    fallbackSuggestions[1].priority = 'high';
  } else {
    fallbackSuggestions[2].priority = 'high';
  }

  return fallbackSuggestions;
}

export async function regenerateSuggestions(
  context: SuggestionContext,
  excludeSuggestions: string[] = []
): Promise<BrandSuggestion[]> {
  const systemPrompt = `You are a brand strategy expert. Generate NEW, different suggestions from any previously provided. Focus on creative and innovative approaches to brand improvement.

  Return suggestions in this exact JSON structure:
  {
    "suggestions": [
      {
        "category": "tone|visuals|messaging|consistency",
        "suggestion": "Specific, actionable advice (1-2 sentences)",
        "priority": "high|medium|low"
      }
    ]
  }`;

  const userPrompt = `Generate 5 fresh, creative brand improvement suggestions. Avoid these previously suggested approaches:
  ${excludeSuggestions.join('\n- ')}

  Brand context:
  - Primary archetype: ${context.brand_archetypes.primary_archetype.name}
  - Consistency score: ${context.consistency_score.overall_score}/100
  - Weakest area: ${getWeakestArea(context.consistency_score)}

  Focus on innovative, creative approaches to brand building. Provide exactly 5 different suggestions.`;

  try {
    const response = await analyzeWithStructuredOutput<{ suggestions: BrandSuggestion[] }>(
      userPrompt,
      systemPrompt
    );

    return response.suggestions || getFallbackSuggestions(context);
  } catch (error) {
    console.error('Suggestion regeneration failed:', error);
    return getFallbackSuggestions(context);
  }
}

function getWeakestArea(score: ConsistencyScore): string {
  const scores = {
    tone: score.subscores.tone,
    visuals: score.subscores.visuals,
    message: score.subscores.message
  };

  return Object.entries(scores).reduce((a, b) => scores[a[0]] < scores[b[0]] ? a : b)[0];
}
