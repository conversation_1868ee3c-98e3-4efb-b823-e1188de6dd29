{"name": "impressly", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "axios": "^1.10.0", "canvas": "^3.1.2", "cheerio": "^1.1.0", "html2canvas": "^1.4.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^4.24.11", "openai": "^5.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}