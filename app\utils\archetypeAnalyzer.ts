import { analyzeWithStructuredOutput } from '../lib/openai';
import { BrandArchetypes, BrandArchetype } from '../types/brand';

const BRAND_ARCHETYPES = {
  Creator: {
    description: 'Innovative, artistic, and imaginative. Values self-expression and creativity.',
    keywords: ['creative', 'innovative', 'artistic', 'original', 'imaginative', 'design', 'build', 'make']
  },
  Explorer: {
    description: 'Adventurous, pioneering, and free-spirited. Seeks new experiences and challenges.',
    keywords: ['adventure', 'explore', 'discover', 'journey', 'freedom', 'pioneer', 'bold', 'travel']
  },
  Caregiver: {
    description: 'Nurturing, compassionate, and helpful. Focused on serving and protecting others.',
    keywords: ['help', 'care', 'support', 'nurture', 'protect', 'serve', 'community', 'family']
  },
  Rebel: {
    description: 'Revolutionary, disruptive, and unconventional. Challenges the status quo.',
    keywords: ['rebel', 'disrupt', 'challenge', 'revolution', 'change', 'break', 'different', 'unconventional']
  },
  Sage: {
    description: 'Wise, knowledgeable, and thoughtful. Seeks truth and shares wisdom.',
    keywords: ['wisdom', 'knowledge', 'learn', 'teach', 'expert', 'insight', 'truth', 'understand']
  },
  Innocent: {
    description: 'Optimistic, pure, and honest. Believes in goodness and simplicity.',
    keywords: ['pure', 'honest', 'simple', 'optimistic', 'good', 'clean', 'fresh', 'natural']
  },
  Hero: {
    description: 'Courageous, determined, and honorable. Rises to challenges and inspires others.',
    keywords: ['courage', 'brave', 'strong', 'leader', 'champion', 'overcome', 'achieve', 'inspire']
  },
  Magician: {
    description: 'Visionary, transformative, and charismatic. Makes dreams come true.',
    keywords: ['transform', 'magic', 'vision', 'dream', 'possibility', 'change', 'inspire', 'create']
  },
  Lover: {
    description: 'Passionate, committed, and intimate. Values relationships and experiences.',
    keywords: ['love', 'passion', 'beauty', 'relationship', 'connection', 'intimate', 'romantic', 'devoted']
  },
  Jester: {
    description: 'Playful, humorous, and spontaneous. Brings joy and lightness to life.',
    keywords: ['fun', 'humor', 'play', 'joy', 'laugh', 'entertaining', 'spontaneous', 'lighthearted']
  },
  Ruler: {
    description: 'Authoritative, responsible, and organized. Seeks control and stability.',
    keywords: ['leader', 'control', 'authority', 'organize', 'responsible', 'power', 'structure', 'stability']
  },
  Everyman: {
    description: 'Relatable, down-to-earth, and authentic. Values belonging and connection.',
    keywords: ['authentic', 'real', 'relatable', 'common', 'belong', 'ordinary', 'genuine', 'practical']
  }
};

export async function analyzeBrandArchetypes(content: string): Promise<BrandArchetypes> {
  const systemPrompt = `You are an expert in brand archetypes and personality analysis. Analyze the provided content and determine which brand archetypes best match the personality and communication style.

  The 12 brand archetypes are:
  1. Creator - Innovative, artistic, imaginative
  2. Explorer - Adventurous, pioneering, free-spirited  
  3. Caregiver - Nurturing, compassionate, helpful
  4. Rebel - Revolutionary, disruptive, unconventional
  5. Sage - Wise, knowledgeable, thoughtful
  6. Innocent - Optimistic, pure, honest
  7. Hero - Courageous, determined, honorable
  8. Magician - Visionary, transformative, charismatic
  9. Lover - Passionate, committed, intimate
  10. Jester - Playful, humorous, spontaneous
  11. Ruler - Authoritative, responsible, organized
  12. Everyman - Relatable, down-to-earth, authentic

  Return the analysis in this exact JSON structure:
  {
    "primary_archetype": {
      "name": "ArchetypeName",
      "percentage": 0-100,
      "description": "Detailed explanation of why this archetype fits"
    },
    "secondary_archetype": {
      "name": "ArchetypeName", 
      "percentage": 0-100,
      "description": "Detailed explanation of why this archetype fits"
    },
    "all_archetypes": [
      {"name": "Creator", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Explorer", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Caregiver", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Rebel", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Sage", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Innocent", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Hero", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Magician", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Lover", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Jester", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Ruler", "percentage": 0-100, "description": "Brief explanation"},
      {"name": "Everyman", "percentage": 0-100, "description": "Brief explanation"}
    ]
  }`;

  const userPrompt = `Analyze this content and determine the brand archetype profile:

  ${content}

  Consider:
  1. The language and tone used
  2. The values and beliefs expressed
  3. The goals and motivations evident
  4. The overall personality that comes through
  5. How the person positions themselves and their work`;

  try {
    const analysis = await analyzeWithStructuredOutput<BrandArchetypes>(
      userPrompt,
      systemPrompt,
      {}
    );

    // Validate and ensure proper structure
    const validatedAnalysis: BrandArchetypes = {
      primary_archetype: {
        name: analysis.primary_archetype?.name || 'Sage',
        percentage: Math.max(0, Math.min(100, analysis.primary_archetype?.percentage || 50)),
        description: analysis.primary_archetype?.description || 'Primary archetype based on content analysis'
      },
      secondary_archetype: analysis.secondary_archetype ? {
        name: analysis.secondary_archetype.name,
        percentage: Math.max(0, Math.min(100, analysis.secondary_archetype.percentage)),
        description: analysis.secondary_archetype.description
      } : undefined,
      all_archetypes: validateAllArchetypes(analysis.all_archetypes)
    };

    return validatedAnalysis;

  } catch (error) {
    console.error('Archetype analysis failed:', error);
    return getFallbackArchetypes();
  }
}

function validateAllArchetypes(archetypes: any[]): BrandArchetype[] {
  const archetypeNames = Object.keys(BRAND_ARCHETYPES);
  const validatedArchetypes: BrandArchetype[] = [];

  archetypeNames.forEach(name => {
    const found = archetypes?.find(a => a.name === name);
    validatedArchetypes.push({
      name,
      percentage: found ? Math.max(0, Math.min(100, found.percentage || 0)) : 0,
      description: found?.description || BRAND_ARCHETYPES[name as keyof typeof BRAND_ARCHETYPES].description
    });
  });

  return validatedArchetypes.sort((a, b) => b.percentage - a.percentage);
}

function getFallbackArchetypes(): BrandArchetypes {
  const allArchetypes = Object.keys(BRAND_ARCHETYPES).map(name => ({
    name,
    percentage: Math.floor(Math.random() * 30) + 10, // Random 10-40%
    description: BRAND_ARCHETYPES[name as keyof typeof BRAND_ARCHETYPES].description
  })).sort((a, b) => b.percentage - a.percentage);

  return {
    primary_archetype: {
      name: 'Sage',
      percentage: 65,
      description: 'Based on available content, shows characteristics of knowledge sharing and expertise'
    },
    secondary_archetype: {
      name: 'Creator',
      percentage: 45,
      description: 'Demonstrates creative and innovative thinking'
    },
    all_archetypes: allArchetypes
  };
}

export function calculateArchetypeConsistency(archetypes: BrandArchetypes[]): number {
  if (archetypes.length < 2) return 100;

  // Calculate consistency based on primary archetype stability
  const primaryArchetypes = archetypes.map(a => a.primary_archetype.name);
  const uniquePrimaries = new Set(primaryArchetypes);
  
  const consistencyScore = ((primaryArchetypes.length - uniquePrimaries.size + 1) / primaryArchetypes.length) * 100;
  
  return Math.round(Math.max(0, Math.min(100, consistencyScore)));
}

export function getArchetypeRecommendations(archetype: BrandArchetype): string[] {
  const recommendations: { [key: string]: string[] } = {
    Creator: [
      'Showcase your creative process and behind-the-scenes content',
      'Use visual storytelling to demonstrate your innovations',
      'Collaborate with other creators to expand your reach'
    ],
    Explorer: [
      'Share your adventures and discoveries',
      'Encourage your audience to try new things',
      'Use dynamic, action-oriented visuals'
    ],
    Caregiver: [
      'Focus on how you help and support others',
      'Share testimonials and success stories',
      'Create educational and helpful content'
    ],
    Rebel: [
      'Challenge conventional thinking in your content',
      'Take bold stances on important issues',
      'Use edgy, attention-grabbing visuals'
    ],
    Sage: [
      'Share valuable insights and knowledge',
      'Position yourself as a thought leader',
      'Create educational content and tutorials'
    ]
  };

  return recommendations[archetype.name] || [
    'Stay authentic to your brand personality',
    'Consistently communicate your core values',
    'Engage meaningfully with your audience'
  ];
}
