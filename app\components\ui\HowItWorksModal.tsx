'use client';

import React from 'react';
import { Modal } from './Modal';

interface HowItWorksModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const HowItWorksModal: React.FC<HowItWorksModalProps> = ({
  isOpen,
  onClose
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="How BrandVibe Works"
      size="lg"
    >
      <div className="space-y-6">
        <div className="text-gray-600 dark:text-gray-300">
          <p className="mb-4">
            BrandVibe uses advanced AI to analyze your digital presence and provide comprehensive brand insights. Here's how it works:
          </p>
        </div>

        <div className="space-y-6">
          {/* Step 1 */}
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
              1
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                🔗 Input Your Digital Presence
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Provide your social media profiles (Instagram, TikTok, LinkedIn), website URL, and optionally upload your logo or brand image. We analyze content from these sources to understand your brand.
              </p>
            </div>
          </div>

          {/* Step 2 */}
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
              2
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                🤖 AI-Powered Analysis
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Our AI analyzes your content using GPT-4 to identify your tone of voice, brand archetypes, visual identity, and messaging patterns. We examine language, color palettes, and communication style.
              </p>
            </div>
          </div>

          {/* Step 3 */}
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
              3
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                📊 Comprehensive Scoring
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Get a detailed consistency score (1-100) with breakdowns for tone, visuals, and messaging. We identify your primary brand archetype and analyze how well your brand elements align.
              </p>
            </div>
          </div>

          {/* Step 4 */}
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
              4
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                💡 Actionable Insights
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Receive personalized suggestions to improve your brand consistency, strengthen your voice, and enhance your visual identity. All recommendations are prioritized and actionable.
              </p>
            </div>
          </div>

          {/* Step 5 */}
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
              5
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                📤 Share Your Results
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                Generate beautiful brand snapshot cards optimized for Instagram, Twitter, and LinkedIn. Share your brand audit results and showcase your professional brand analysis.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
            🔒 Privacy & Security
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            We only analyze publicly available content from the URLs you provide. No personal data is stored permanently, and all analysis is processed securely using industry-standard encryption.
          </p>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
            ⚡ Quick & Accurate
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Most brand audits complete in under 60 seconds. Our AI is trained on thousands of brand examples to provide accurate, professional-grade analysis that would typically cost hundreds of dollars from a consultant.
          </p>
        </div>
      </div>
    </Modal>
  );
};
