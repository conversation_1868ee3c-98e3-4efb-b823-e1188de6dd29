'use client';

import { useState, useEffect } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { BrandAuditResult, ShareableCard, BrandSuggestion } from '../types/brand';
import { generateBrandCard, downloadCard } from '../utils/cardGenerator';

export default function ResultsPage() {
  const [results, setResults] = useState<BrandAuditResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [generatingCard, setGeneratingCard] = useState(false);
  const [shareableCards, setShareableCards] = useState<ShareableCard[]>([]);
  const [regeneratingSuggestions, setRegeneratingSuggestions] = useState(false);
  const [currentSuggestions, setCurrentSuggestions] = useState<BrandSuggestion[]>([]);

  useEffect(() => {
    // Load results from localStorage
    const storedResults = localStorage.getItem('brandAuditResults');

    if (storedResults) {
      try {
        const parsedResults = JSON.parse(storedResults);
        setResults(parsedResults);
        setCurrentSuggestions(parsedResults.suggestions || []);
      } catch (error) {
        console.error('Failed to parse stored results:', error);
      }
    }

    setLoading(false);
  }, []);

  const handleGenerateCard = async (format: 'instagram' | 'twitter' | 'linkedin') => {
    if (!results) return;

    setGeneratingCard(true);
    try {
      const card = await generateBrandCard(results, format);
      setShareableCards(prev => {
        const filtered = prev.filter(c => c.format !== format);
        return [...filtered, card];
      });
    } catch (error) {
      console.error('Card generation failed:', error);
      alert('Failed to generate card. Please try again.');
    } finally {
      setGeneratingCard(false);
    }
  };

  const handleRegenerateSuggestions = async () => {
    if (!results) return;

    setRegeneratingSuggestions(true);
    try {
      const excludeSuggestions = currentSuggestions.map(s => s.suggestion);
      const context = {
        tone_analysis: results.tone_analysis,
        visual_identity: results.visual_identity,
        brand_archetypes: results.brand_archetypes,
        consistency_score: results.consistency_score,
        content_sources: [] // We don't have this in stored results, but it's optional
      };

      const response = await fetch('/api/regenerate-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ context, excludeSuggestions }),
      });

      if (!response.ok) {
        throw new Error('Failed to regenerate suggestions');
      }

      const data = await response.json();
      const newSuggestions = data.suggestions;

      setCurrentSuggestions(newSuggestions);

      // Update stored results with new suggestions
      const updatedResults = { ...results, suggestions: newSuggestions };
      localStorage.setItem('brandAuditResults', JSON.stringify(updatedResults));
      setResults(updatedResults);

    } catch (error) {
      console.error('Suggestion regeneration failed:', error);
      alert('Failed to generate new suggestions. Please try again.');
    } finally {
      setRegeneratingSuggestions(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading your brand audit results...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <Card className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No Results Found
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            We couldn&apos;t find your brand audit results.
          </p>
          <Button onClick={() => window.location.href = '/'}>
            Start New Audit
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="border-b bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BV</span>
              </div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                BrandVibe Results
              </h1>
            </div>
            <Button variant="outline" onClick={() => window.location.href = '/'}>
              New Audit
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overall Score */}
        <Card className="mb-8 text-center">
          <div className="mb-4">
            <div className="text-6xl font-bold text-transparent bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text">
              {results.consistency_score.overall_score}
            </div>
            <div className="text-xl text-gray-600 dark:text-gray-300">
              Overall Brand Consistency Score
            </div>
          </div>
          
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                {results.consistency_score.subscores.tone}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Tone</div>
            </div>
            <div>
              <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                {results.consistency_score.subscores.visuals}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Visuals</div>
            </div>
            <div>
              <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                {results.consistency_score.subscores.message}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Message</div>
            </div>
          </div>
        </Card>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Tone Analysis */}
          <Card>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🎯 Tone & Voice Analysis
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {results.tone_analysis.tone_summary}
            </p>
            
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-gray-900 dark:text-white">Communication Style</span>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {results.tone_analysis.sliders.formal_casual < 30 ? 'Very Formal' :
                     results.tone_analysis.sliders.formal_casual < 50 ? 'Formal' :
                     results.tone_analysis.sliders.formal_casual < 70 ? 'Balanced' :
                     results.tone_analysis.sliders.formal_casual < 85 ? 'Casual' : 'Very Casual'}
                  </span>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Formal</span>
                  <span>Casual</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full"
                    style={{ width: `${results.tone_analysis.sliders.formal_casual}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-gray-900 dark:text-white">Motivational Approach</span>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {results.tone_analysis.sliders.inspiring_humble < 30 ? 'Very Inspiring' :
                     results.tone_analysis.sliders.inspiring_humble < 50 ? 'Inspiring' :
                     results.tone_analysis.sliders.inspiring_humble < 70 ? 'Balanced' :
                     results.tone_analysis.sliders.inspiring_humble < 85 ? 'Humble' : 'Very Humble'}
                  </span>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Inspiring</span>
                  <span>Humble</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full"
                    style={{ width: `${results.tone_analysis.sliders.inspiring_humble}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-gray-900 dark:text-white">Relationship Style</span>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {results.tone_analysis.sliders.friendly_professional < 30 ? 'Very Friendly' :
                     results.tone_analysis.sliders.friendly_professional < 50 ? 'Friendly' :
                     results.tone_analysis.sliders.friendly_professional < 70 ? 'Balanced' :
                     results.tone_analysis.sliders.friendly_professional < 85 ? 'Professional' : 'Very Professional'}
                  </span>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mb-1">
                  <span>Friendly</span>
                  <span>Professional</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full"
                    style={{ width: `${results.tone_analysis.sliders.friendly_professional}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="mt-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Common Phrases:</h4>
              <div className="flex flex-wrap gap-2">
                {results.tone_analysis.common_phrases.map((phrase, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full text-sm"
                  >
                    "                    &ldquo;{phrase}&rdquo;"
                  </span>
                ))}
              </div>
            </div>
          </Card>

          {/* Brand Archetypes */}
          <Card>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🏛️ Brand Archetypes
            </h3>
            
            <div className="space-y-4">
              <div className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {results.brand_archetypes.primary_archetype.name}
                  </h4>
                  <span className="text-lg font-bold text-purple-600">
                    {results.brand_archetypes.primary_archetype.percentage}%
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {results.brand_archetypes.primary_archetype.description}
                </p>
              </div>

              {results.brand_archetypes.secondary_archetype && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      {results.brand_archetypes.secondary_archetype.name}
                    </h4>
                    <span className="text-lg font-bold text-blue-600">
                      {results.brand_archetypes.secondary_archetype.percentage}%
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {results.brand_archetypes.secondary_archetype.description}
                  </p>
                </div>
              )}

              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">All Archetypes:</h4>
                <div className="space-y-2">
                  {results.brand_archetypes.all_archetypes.map((archetype, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-300">
                        {archetype.name}
                      </span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full"
                            style={{ width: `${archetype.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-900 dark:text-white w-8">
                          {archetype.percentage}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Visual Identity & Suggestions */}
        <div className="grid lg:grid-cols-2 gap-8 mt-8">
          {/* Visual Identity */}
          <Card>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🎨 Visual Identity
            </h3>
            
            <div className="mb-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Color Palette:</h4>
              <div className="flex space-x-2 mb-2">
                {results.visual_identity.color_palette.colors.map((color, index) => (
                  <div
                    key={index}
                    className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                    style={{ backgroundColor: color }}
                    title={color}
                  ></div>
                ))}
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {results.visual_identity.color_emotion_analysis}
              </p>
            </div>

            {results.visual_identity.similar_brand_match && (
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Similar Brand Style:</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {results.visual_identity.similar_brand_match}
                </p>
              </div>
            )}
          </Card>

          {/* Suggestions */}
          <Card>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              💡 Personalized Suggestions
            </h3>
            
            <div className="space-y-3">
              {currentSuggestions.map((suggestion, index) => (
                <div 
                  key={index}
                  className={`p-3 rounded-lg border-l-4 ${
                    suggestion.priority === 'high' 
                      ? 'border-red-500 bg-red-50 dark:bg-red-900/20' 
                      : suggestion.priority === 'medium'
                      ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
                      : 'border-green-500 bg-green-50 dark:bg-green-900/20'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                      {suggestion.category}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      suggestion.priority === 'high' 
                        ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' 
                        : suggestion.priority === 'medium'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                        : 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                    }`}>
                      {suggestion.priority}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {suggestion.suggestion}
                  </p>
                </div>
              ))}
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                loading={regeneratingSuggestions}
                onClick={handleRegenerateSuggestions}
              >
                {regeneratingSuggestions ? 'Generating...' : 'Generate New Suggestions'}
              </Button>
            </div>
          </Card>
        </div>

        {/* Share Section */}
        <Card className="mt-8 text-center">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            📤 Share Your Brand Snapshot
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Create a shareable visual summary of your brand audit results
          </p>
          <div className="flex justify-center space-x-4 mb-4">
            <Button
              variant="outline"
              loading={generatingCard}
              onClick={() => handleGenerateCard('instagram')}
            >
              Instagram Story
            </Button>
            <Button
              variant="outline"
              loading={generatingCard}
              onClick={() => handleGenerateCard('twitter')}
            >
              Twitter Post
            </Button>
            <Button
              variant="outline"
              loading={generatingCard}
              onClick={() => handleGenerateCard('linkedin')}
            >
              LinkedIn Post
            </Button>
          </div>

          {shareableCards.length > 0 && (
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                Generated cards ready for download:
              </p>
              <div className="flex justify-center space-x-2">
                {shareableCards.map((card, index) => (
                  <Button
                    key={index}
                    size="sm"
                    variant="secondary"
                    onClick={() => downloadCard(card)}
                  >
                    Download {card.format}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </Card>
      </main>
    </div>
  );
}
