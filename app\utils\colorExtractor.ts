import ColorThief from 'colorthief';

export interface ExtractedColors {
  dominant: string;
  palette: string[];
  emotions: string[];
}

export function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

export function getColorEmotion(hex: string): string[] {
  const colorEmotions: { [key: string]: string[] } = {
    // Reds
    '#FF0000': ['passionate', 'energetic', 'bold'],
    '#DC143C': ['intense', 'dramatic', 'powerful'],
    '#B22222': ['strong', 'confident', 'assertive'],
    
    // Blues
    '#0000FF': ['trustworthy', 'professional', 'calm'],
    '#4169E1': ['reliable', 'stable', 'corporate'],
    '#87CEEB': ['peaceful', 'serene', 'gentle'],
    
    // Greens
    '#008000': ['natural', 'growth', 'harmony'],
    '#32CD32': ['fresh', 'vibrant', 'healthy'],
    '#228B22': ['stable', 'balanced', 'grounded'],
    
    // Yellows
    '#FFFF00': ['optimistic', 'cheerful', 'creative'],
    '#FFD700': ['luxurious', 'premium', 'valuable'],
    '#F0E68C': ['warm', 'friendly', 'approachable'],
    
    // Purples
    '#800080': ['creative', 'mysterious', 'sophisticated'],
    '#9370DB': ['artistic', 'imaginative', 'unique'],
    '#DDA0DD': ['gentle', 'romantic', 'dreamy'],
    
    // Oranges
    '#FFA500': ['energetic', 'enthusiastic', 'fun'],
    '#FF4500': ['bold', 'adventurous', 'exciting'],
    '#FFB347': ['warm', 'inviting', 'friendly'],
    
    // Neutrals
    '#000000': ['sophisticated', 'elegant', 'powerful'],
    '#FFFFFF': ['clean', 'pure', 'minimalist'],
    '#808080': ['balanced', 'neutral', 'professional']
  };

  // Find closest color match
  const hexUpper = hex.toUpperCase();
  if (colorEmotions[hexUpper]) {
    return colorEmotions[hexUpper];
  }

  // Basic color categorization based on RGB values
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);

  if (r > g && r > b) return ['passionate', 'energetic', 'bold'];
  if (g > r && g > b) return ['natural', 'growth', 'harmony'];
  if (b > r && b > g) return ['trustworthy', 'calm', 'professional'];
  if (r + g + b < 100) return ['sophisticated', 'elegant', 'mysterious'];
  if (r + g + b > 600) return ['clean', 'pure', 'bright'];
  
  return ['balanced', 'neutral', 'versatile'];
}

export async function extractColorsFromImage(imageFile: File): Promise<ExtractedColors> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    img.onload = () => {
      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      try {
        const colorThief = new ColorThief();
        
        // Get dominant color
        const dominantRgb = colorThief.getColor(img);
        const dominant = rgbToHex(dominantRgb[0], dominantRgb[1], dominantRgb[2]);
        
        // Get color palette
        const paletteRgb = colorThief.getPalette(img, 5);
        const palette = paletteRgb.map(rgb => rgbToHex(rgb[0], rgb[1], rgb[2]));
        
        // Get emotions for all colors
        const emotions = [...new Set(palette.flatMap(getColorEmotion))];

        resolve({
          dominant,
          palette,
          emotions
        });
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(imageFile);
  });
}

export async function extractColorsFromUrl(imageUrl: string): Promise<ExtractedColors> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const colorThief = new ColorThief();
        
        const dominantRgb = colorThief.getColor(img);
        const dominant = rgbToHex(dominantRgb[0], dominantRgb[1], dominantRgb[2]);
        
        const paletteRgb = colorThief.getPalette(img, 5);
        const palette = paletteRgb.map(rgb => rgbToHex(rgb[0], rgb[1], rgb[2]));
        
        const emotions = [...new Set(palette.flatMap(getColorEmotion))];

        resolve({
          dominant,
          palette,
          emotions
        });
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => reject(new Error('Failed to load image from URL'));
    img.src = imageUrl;
  });
}
