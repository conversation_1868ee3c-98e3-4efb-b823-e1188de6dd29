// ColorThief removed - using manual color extraction instead

export interface ExtractedColors {
  dominant: string;
  palette: string[];
  emotions: string[];
}

export function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

export function getColorEmotion(hex: string): string[] {
  const colorEmotions: { [key: string]: string[] } = {
    // Reds
    '#FF0000': ['passionate', 'energetic', 'bold'],
    '#DC143C': ['intense', 'dramatic', 'powerful'],
    '#B22222': ['strong', 'confident', 'assertive'],
    
    // Blues
    '#0000FF': ['trustworthy', 'professional', 'calm'],
    '#4169E1': ['reliable', 'stable', 'corporate'],
    '#87CEEB': ['peaceful', 'serene', 'gentle'],
    
    // Greens
    '#008000': ['natural', 'growth', 'harmony'],
    '#32CD32': ['fresh', 'vibrant', 'healthy'],
    '#228B22': ['stable', 'balanced', 'grounded'],
    
    // Yellows
    '#FFFF00': ['optimistic', 'cheerful', 'creative'],
    '#FFD700': ['luxurious', 'premium', 'valuable'],
    '#F0E68C': ['warm', 'friendly', 'approachable'],
    
    // Purples
    '#800080': ['creative', 'mysterious', 'sophisticated'],
    '#9370DB': ['artistic', 'imaginative', 'unique'],
    '#DDA0DD': ['gentle', 'romantic', 'dreamy'],
    
    // Oranges
    '#FFA500': ['energetic', 'enthusiastic', 'fun'],
    '#FF4500': ['bold', 'adventurous', 'exciting'],
    '#FFB347': ['warm', 'inviting', 'friendly'],
    
    // Neutrals
    '#000000': ['sophisticated', 'elegant', 'powerful'],
    '#FFFFFF': ['clean', 'pure', 'minimalist'],
    '#808080': ['balanced', 'neutral', 'professional']
  };

  // Find closest color match
  const hexUpper = hex.toUpperCase();
  if (colorEmotions[hexUpper]) {
    return colorEmotions[hexUpper];
  }

  // Basic color categorization based on RGB values
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);

  if (r > g && r > b) return ['passionate', 'energetic', 'bold'];
  if (g > r && g > b) return ['natural', 'growth', 'harmony'];
  if (b > r && b > g) return ['trustworthy', 'calm', 'professional'];
  if (r + g + b < 100) return ['sophisticated', 'elegant', 'mysterious'];
  if (r + g + b > 600) return ['clean', 'pure', 'bright'];
  
  return ['balanced', 'neutral', 'versatile'];
}

export async function extractColorsFromImage(imageFile: File): Promise<ExtractedColors> {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    console.warn('Color extraction attempted on server side, returning fallback colors');
    return {
      dominant: '#4f46e5',
      palette: ['#4f46e5', '#7c3aed', '#0891b2', '#059669', '#dc2626'],
      emotions: ['professional', 'modern', 'trustworthy']
    };
  }

  return new Promise((resolve) => {
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    const fallbackColors = {
      dominant: '#4f46e5',
      palette: ['#4f46e5', '#7c3aed', '#0891b2', '#059669', '#dc2626'],
      emotions: ['professional', 'modern', 'trustworthy']
    };

    img.onload = () => {
      if (!ctx) {
        console.error('Could not get canvas context');
        resolve(fallbackColors);
        return;
      }

      try {
        // Resize image for better performance
        const maxSize = 200;
        const ratio = Math.min(maxSize / img.width, maxSize / img.height);
        canvas.width = img.width * ratio;
        canvas.height = img.height * ratio;

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Get image data for manual color analysis
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const colors = extractDominantColors(imageData);

        // Get emotions for all colors
        const emotions = [...new Set(colors.flatMap(getColorEmotion))];

        console.log('Successfully extracted colors:', colors);
        resolve({
          dominant: colors[0],
          palette: colors,
          emotions
        });
      } catch (error) {
        console.error('Color extraction error:', error);
        resolve(fallbackColors);
      }
    };

    img.onerror = (error) => {
      console.error('Failed to load image:', error);
      resolve(fallbackColors);
    };

    try {
      img.src = URL.createObjectURL(imageFile);
    } catch (error) {
      console.error('Failed to create object URL:', error);
      resolve(fallbackColors);
    }
  });
}

function extractDominantColors(imageData: ImageData): string[] {
  const data = imageData.data;
  const colorCounts: { [key: string]: number } = {};

  // Sample every 8th pixel for better performance
  for (let i = 0; i < data.length; i += 32) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const a = data[i + 3];

    // Skip transparent pixels
    if (a < 128) continue;

    // Skip very light or very dark pixels
    const brightness = (r + g + b) / 3;
    if (brightness < 40 || brightness > 215) continue;

    // Quantize colors to reduce noise (group similar colors)
    const quantizedR = Math.round(r / 32) * 32;
    const quantizedG = Math.round(g / 32) * 32;
    const quantizedB = Math.round(b / 32) * 32;

    const hex = rgbToHex(quantizedR, quantizedG, quantizedB);
    colorCounts[hex] = (colorCounts[hex] || 0) + 1;
  }

  // Get top colors and filter out similar ones
  const sortedColors = Object.entries(colorCounts)
    .sort(([,a], [,b]) => b - a)
    .map(([color]) => color);

  // Filter out similar colors
  const distinctColors: string[] = [];
  for (const color of sortedColors) {
    const isSimilar = distinctColors.some(existing =>
      colorDistance(color, existing) < 50 // Threshold for similarity
    );
    if (!isSimilar) {
      distinctColors.push(color);
    }
    if (distinctColors.length >= 5) break;
  }

  // If we don't have enough colors, add some complementary defaults
  const defaultColors = ['#4f46e5', '#7c3aed', '#0891b2', '#059669', '#dc2626'];
  while (distinctColors.length < 5) {
    const defaultColor = defaultColors[distinctColors.length];
    if (!distinctColors.includes(defaultColor)) {
      distinctColors.push(defaultColor);
    }
  }

  return distinctColors;
}

function colorDistance(color1: string, color2: string): number {
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');

  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);

  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);

  return Math.sqrt(
    Math.pow(r2 - r1, 2) + Math.pow(g2 - g1, 2) + Math.pow(b2 - b1, 2)
  );
}

// extractColorsFromUrl function removed - not needed for current implementation
