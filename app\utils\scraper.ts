import axios from 'axios';
import * as cheerio from 'cheerio';

export interface ScrapedContent {
  title?: string;
  description?: string;
  text_content: string;
  meta_description?: string;
  og_description?: string;
}

export async function scrapeWebsite(url: string): Promise<ScrapedContent> {
  try {
    // Add protocol if missing
    const fullUrl = url.startsWith('http') ? url : `https://${url}`;
    
    const response = await axios.get(fullUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);
    
    // Remove script and style elements
    $('script, style, nav, footer, header').remove();
    
    const title = $('title').text().trim();
    const metaDescription = $('meta[name="description"]').attr('content');
    const ogDescription = $('meta[property="og:description"]').attr('content');
    
    // Extract main text content
    const textContent = $('body').text()
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 5000); // Limit to first 5000 characters

    return {
      title,
      description: metaDescription || ogDescription,
      text_content: textContent,
      meta_description: metaDescription,
      og_description: ogDescription
    };
  } catch (error) {
    console.error('Website scraping error:', error);
    throw new Error(`Failed to scrape website: ${url}`);
  }
}

export function extractInstagramHandle(url: string): string | null {
  const match = url.match(/instagram\.com\/([^\/\?]+)/);
  return match ? match[1] : null;
}

export function extractTikTokHandle(url: string): string | null {
  const match = url.match(/tiktok\.com\/@([^\/\?]+)/);
  return match ? match[1] : null;
}

export function extractLinkedInHandle(url: string): string | null {
  const match = url.match(/linkedin\.com\/in\/([^\/\?]+)/);
  return match ? match[1] : null;
}

export function validateUrl(url: string): boolean {
  try {
    new URL(url.startsWith('http') ? url : `https://${url}`);
    return true;
  } catch {
    return false;
  }
}
