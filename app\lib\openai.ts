import OpenAI from 'openai';

if (!process.env.OPENAI_API_KEY) {
  throw new Error('OPENAI_API_KEY is not set in environment variables');
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function analyzeWithGPT(prompt: string, systemPrompt?: string) {
  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini', // More cost-effective and reliable
      messages: [
        ...(systemPrompt ? [{ role: 'system' as const, content: systemPrompt }] : []),
        { role: 'user' as const, content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    return response.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('OpenAI API Error:', error);

    // Fallback to gpt-4 if gpt-4o-mini fails
    try {
      const fallbackResponse = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          ...(systemPrompt ? [{ role: 'system' as const, content: systemPrompt }] : []),
          { role: 'user' as const, content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      return fallbackResponse.choices[0]?.message?.content || '';
    } catch (fallbackError) {
      console.error('OpenAI Fallback API Error:', fallbackError);
      throw new Error('Failed to analyze with AI');
    }
  }
}

export async function analyzeWithStructuredOutput<T>(
  prompt: string,
  systemPrompt: string
): Promise<T> {
  try {
    // Use gpt-4o which supports JSON mode, or fall back to regular gpt-4
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini', // This model supports JSON mode and is more cost-effective
      messages: [
        { role: 'system', content: systemPrompt + '\n\nIMPORTANT: Respond with valid JSON only.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.3,
      max_tokens: 2000,
      response_format: { type: 'json_object' }
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    return JSON.parse(content) as T;
  } catch (error) {
    console.error('OpenAI Structured Analysis Error:', error);

    // Fallback: try with regular gpt-4 without JSON mode
    try {
      const fallbackResponse = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt + '\n\nIMPORTANT: Respond with valid JSON only. Do not include any text before or after the JSON.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const fallbackContent = fallbackResponse.choices[0]?.message?.content;
      if (!fallbackContent) {
        throw new Error('No response from OpenAI fallback');
      }

      // Try to extract JSON from the response
      const jsonMatch = fallbackContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]) as T;
      } else {
        return JSON.parse(fallbackContent) as T;
      }
    } catch (fallbackError) {
      console.error('OpenAI Fallback Analysis Error:', fallbackError);
      throw new Error('Failed to analyze with AI - both primary and fallback methods failed');
    }
  }
}
