'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { FileUpload } from '../ui/FileUpload';
import { Card } from '../ui/Card';
import { validateUrl } from '../../utils/scraper';
import { extractColorsFromImage } from '../../utils/colorExtractor';

const brandAuditSchema = z.object({
  instagram_url: z.string().optional().refine((val) => !val || validateUrl(val), {
    message: 'Please enter a valid Instagram URL'
  }),
  tiktok_url: z.string().optional().refine((val) => !val || validateUrl(val), {
    message: 'Please enter a valid TikTok URL'
  }),
  linkedin_url: z.string().optional().refine((val) => !val || validateUrl(val), {
    message: 'Please enter a valid LinkedIn URL'
  }),
  website_url: z.string().optional().refine((val) => !val || validateUrl(val), {
    message: 'Please enter a valid website URL'
  })
}).refine((data) => {
  return data.instagram_url || data.tiktok_url || data.linkedin_url || data.website_url;
}, {
  message: 'Please provide at least one social media profile or website URL',
  path: ['instagram_url']
});

type BrandAuditFormData = z.infer<typeof brandAuditSchema>;

interface ExtractedColors {
  dominant: string;
  palette: string[];
  emotions: string[];
}

interface BrandAuditFormProps {
  onSubmit: (data: BrandAuditFormData & { image_upload?: File; extracted_colors?: ExtractedColors }) => void;
  loading?: boolean;
}

export const BrandAuditForm: React.FC<BrandAuditFormProps> = ({
  onSubmit,
  loading = false
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<BrandAuditFormData>({
    resolver: zodResolver(brandAuditSchema)
  });

  const watchedFields = watch();
  const hasAnyUrl = Object.values(watchedFields).some(value => value && value.length > 0);

  const handleFileSelect = (file: File | null) => {
    setUploadedFile(file);
    setFileError('');
    
    if (file && file.size > 5 * 1024 * 1024) {
      setFileError('File size must be less than 5MB');
      setUploadedFile(null);
    }
  };

  const onFormSubmit = async (data: BrandAuditFormData) => {
    let extractedColors = null;

    // Extract colors from uploaded image on client side
    if (uploadedFile) {
      try {
        extractedColors = await extractColorsFromImage(uploadedFile);
        console.log('Extracted colors:', extractedColors);
      } catch (error) {
        console.error('Color extraction failed:', error);
        // Continue without colors - server will use fallback
      }
    }

    onSubmit({
      ...data,
      ...(uploadedFile && { image_upload: uploadedFile }),
      ...(extractedColors && { extracted_colors: extractedColors })
    });
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Start Your Brand Audit
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Provide your social media profiles and website to get started. We'll analyze your content and visual identity.
        </p>
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        {/* Social Media URLs */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Social Media Profiles
          </h3>
          
          <Input
            {...register('instagram_url')}
            label="Instagram Profile"
            placeholder="https://instagram.com/yourusername"
            error={errors.instagram_url?.message}
            helperText="Your Instagram profile URL"
          />

          <Input
            {...register('tiktok_url')}
            label="TikTok Profile"
            placeholder="https://tiktok.com/@yourusername"
            error={errors.tiktok_url?.message}
            helperText="Your TikTok profile URL"
          />

          <Input
            {...register('linkedin_url')}
            label="LinkedIn Profile"
            placeholder="https://linkedin.com/in/yourusername"
            error={errors.linkedin_url?.message}
            helperText="Your LinkedIn profile URL"
          />
        </div>

        {/* Website URL */}
        <div>
          <Input
            {...register('website_url')}
            label="Website"
            placeholder="https://yourwebsite.com"
            error={errors.website_url?.message}
            helperText="Your personal website or portfolio"
          />
        </div>

        {/* Image Upload */}
        <div>
          <FileUpload
            onFileSelect={handleFileSelect}
            label="Logo or Brand Image (Optional)"
            error={fileError}
            accept="image/*"
            maxSize={5}
          />
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Upload your logo, profile picture, or any image that represents your brand
          </p>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            size="lg"
            loading={loading}
            disabled={!hasAnyUrl}
            className="w-full"
          >
            {loading ? 'Analyzing Your Brand...' : 'Start Brand Audit'}
          </Button>
          
          {!hasAnyUrl && (
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 text-center">
              Please provide at least one social media profile or website URL
            </p>
          )}
        </div>
      </form>
    </Card>
  );
};
