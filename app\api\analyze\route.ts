import { NextRequest, NextResponse } from 'next/server';
import { scrapeWebsite } from '../../utils/scraper';
import { analyzeToneAndVoice } from '../../utils/toneAnalyzer';
import { analyzeVisualIdentity } from '../../utils/visualAnalyzer';
import { analyzeBrandArchetypes } from '../../utils/archetypeAnalyzer';
import { calculateBrandConsistency } from '../../utils/consistencyScorer';
import { generatePersonalizedSuggestions } from '../../utils/suggestionsEngine';
import { BrandAuditResult } from '../../types/brand';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    
    const instagram_url = formData.get('instagram_url') as string;
    const tiktok_url = formData.get('tiktok_url') as string;
    const linkedin_url = formData.get('linkedin_url') as string;
    const website_url = formData.get('website_url') as string;
    const image_upload = formData.get('image_upload') as File;

    // Collect content from provided URLs
    const contentSources = [];
    
    if (website_url) {
      try {
        const websiteContent = await scrapeWebsite(website_url);
        contentSources.push({
          source: 'website',
          url: website_url,
          content: websiteContent.text_content,
          title: websiteContent.title,
          description: websiteContent.description
        });
      } catch (error) {
        console.error('Website scraping failed:', error);
      }
    }

    // For social media, we'll simulate content analysis for now
    // In a real implementation, you'd use social media APIs
    if (instagram_url) {
      contentSources.push({
        source: 'instagram',
        url: instagram_url,
        content: 'Instagram profile content would be analyzed here',
        bio: 'Sample Instagram bio content'
      });
    }

    if (linkedin_url) {
      contentSources.push({
        source: 'linkedin',
        url: linkedin_url,
        content: 'LinkedIn profile content would be analyzed here',
        bio: 'Sample LinkedIn bio content'
      });
    }

    if (tiktok_url) {
      contentSources.push({
        source: 'tiktok',
        url: tiktok_url,
        content: 'TikTok profile content would be analyzed here',
        bio: 'Sample TikTok bio content'
      });
    }

    if (contentSources.length === 0) {
      return NextResponse.json(
        { error: 'No valid content sources provided' },
        { status: 400 }
      );
    }

    // Prepare content for analysis
    const contentForAnalysis = contentSources.map(source =>
      `Source: ${source.source}\nURL: ${source.url}\nContent: ${source.content}`
    ).join('\n\n---\n\n');

    // Run all analyses
    const [toneAnalysis, visualIdentity, brandArchetypes] = await Promise.all([
      analyzeToneAndVoice(contentForAnalysis),
      analyzeVisualIdentity(image_upload, website_url),
      analyzeBrandArchetypes(contentForAnalysis)
    ]);

    // Calculate consistency score
    const consistencyScore = calculateBrandConsistency({
      tone_analysis: toneAnalysis,
      visual_identity: visualIdentity,
      brand_archetypes: brandArchetypes,
      content_sources: contentSources
    });

    // Generate personalized suggestions
    const suggestions = await generatePersonalizedSuggestions({
      tone_analysis: toneAnalysis,
      visual_identity: visualIdentity,
      brand_archetypes: brandArchetypes,
      consistency_score: consistencyScore,
      content_sources: contentSources
    });

    // Create comprehensive result
    const result: BrandAuditResult = {
      id: `audit_${Date.now()}`,
      inputs: {
        instagram_url,
        tiktok_url,
        linkedin_url,
        website_url,
        ...(image_upload && { image_upload: image_upload })
      },
      tone_analysis: toneAnalysis,
      visual_identity: visualIdentity,
      brand_archetypes: brandArchetypes,
      consistency_score: consistencyScore,
      suggestions: suggestions,
      created_at: new Date()
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('Brand analysis error:', error);
    return NextResponse.json(
      { error: 'Failed to analyze brand. Please try again.' },
      { status: 500 }
    );
  }
}
