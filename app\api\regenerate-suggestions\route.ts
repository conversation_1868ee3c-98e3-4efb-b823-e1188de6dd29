import { NextRequest, NextResponse } from 'next/server';
import { regenerateSuggestions } from '../../utils/suggestionsEngine';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { context, excludeSuggestions } = body;

    if (!context) {
      return NextResponse.json(
        { error: 'Context is required for suggestion regeneration' },
        { status: 400 }
      );
    }

    const newSuggestions = await regenerateSuggestions(context, excludeSuggestions || []);

    return NextResponse.json({ suggestions: newSuggestions });

  } catch (error) {
    console.error('Suggestion regeneration error:', error);
    return NextResponse.json(
      { error: 'Failed to regenerate suggestions. Please try again.' },
      { status: 500 }
    );
  }
}
